import { getDb } from "@database";
import { errorLogs } from "@database/schema";
import { and, eq, gte } from "drizzle-orm";

/**
 * Global error logging utility that prevents duplicate errors
 * Integrates with the errorLogs table schema for comprehensive error tracking
 */
const DUPLICATE_WINDOW_SECONDS = 30; // Prevent duplicates within 30 seconds

/**
 * Log an error to the error_logs table (prevents duplicates)
 * @param type - Error type/context
 * @param error - Error object
 * @param metadata - Additional metadata
 * @param service - Service name that generated the error
 */
export async function logError(
	type: string,
	error: unknown,
	metadata?: Record<string, unknown>,
	service?: string,
): Promise<void> {
	try {
		const errorMessage = error instanceof Error ? error.message : String(error);
		const errorStack = error instanceof Error ? error.stack : undefined;

		// Check if this exact error was already logged recently
		const duplicateWindow = new Date(
			Date.now() - DUPLICATE_WINDOW_SECONDS * 1000,
		);

		const db = getDb();
		const existingError = await db
			.select()
			.from(errorLogs)
			.where(
				and(
					eq(errorLogs.type, type),
					eq(errorLogs.message, errorMessage),
					gte(errorLogs.createdAt, duplicateWindow),
				),
			)
			.limit(1);

		if (existingError.length > 0) {
			console.log(
				`Error already logged recently, skipping duplicate: ${type} - ${errorMessage}`,
			);
			return;
		}

		await db.insert(errorLogs).values({
			type,
			message: errorMessage,
			stack: errorStack,
			data: {
				timestamp: new Date().toISOString(),
				service: service || "Unknown",
				metadata,
				error:
					error instanceof Error
						? {
								name: error.name,
								message: error.message,
								stack: error.stack,
							}
						: error,
			},
		});

		console.log(`Error logged: ${type} - ${errorMessage}`);
	} catch (logError) {
		// If error logging fails, at least log to console
		console.error("Failed to log error to database:", logError);
		console.error("Original error:", error);
	}
}

/**
 * Log a sync error with patient/contact context
 * @param type - Error type
 * @param error - Error object
 * @param patientId - Patient/Contact ID
 * @param platform - Platform (CC or AP)
 * @param service - Service name
 */
export async function logSyncError(
	type: string,
	error: unknown,
	patientId: string | number,
	platform: "CC" | "AP",
	service: string,
): Promise<void> {
	await logError(
		type,
		error,
		{
			patientId,
			platform,
			syncDirection: `${platform} -> ${platform === "CC" ? "AP" : "CC"}`,
		},
		service,
	);
}

/**
 * Log an API error
 * @param type - Error type
 * @param error - Error object
 * @param endpoint - API endpoint
 * @param method - HTTP method
 * @param service - Service name
 */
export async function logApiError(
	type: string,
	error: unknown,
	endpoint: string,
	method: string,
	service: string,
): Promise<void> {
	await logError(
		type,
		error,
		{
			endpoint,
			method,
			apiError: true,
		},
		service,
	);
}
